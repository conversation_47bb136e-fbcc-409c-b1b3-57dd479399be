<template>
  <div class="common_box">
      <div class="form_box_btn" style="">
        <a-button type="primary" @click="handleAllocation()">激励政策配置</a-button>
      </div>
      <a-tabs @change="handleTabChange" :activeKey="activeKey">
        <a-tab-pane key="1" tab="月度业绩达成激励汇总表" >
          <BasicTable
            @register="registerMonthTable"
            isMenuTable ref="tableMonthRef"
            :row-selection="{ 
              selectedRowKeys: selectedKeysMonth, 
              onChange: onSelectChangeMonth,
              getCheckboxProps: (record) => ({
                 disabled: !(record.yesOrNo === '0' && record._originalContractTarget === null && record.modifyUserId !== null)
              })
            }"
          >
            <template #toolbar>
              <template v-for="button in tableButtonConfig" :key="button.code">
                <a-button
                  v-if="button.isDefault"
                  type="primary"
                  v-auth="`performanceIncentive:${button.code}`"
                  @click="buttonClick(button.code)"
                >
                  <template #icon><Icon :icon="button.icon" /></template>
                  {{ button.name }}
                </a-button>
                <a-button v-else type="primary">
                  <template #icon><Icon :icon="button.icon" /></template>
                  {{ button.name }}
                </a-button>
              </template>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'action'">
                <TableAction :actions="getActions(record)" />
              </template>
              <template v-if="column.dataIndex === 'contractTarget'">
                <div class="editable-cell">
                  <a-input-number
                    v-model:value="record.contractTarget"
                    placeholder="请输入"
                    :min="0"
                    :disabled="!(record.yesOrNo === '0' && record._originalContractTarget === null && record.modifyUserId !== null)"
                  />
                </div>
              </template>
              <template v-else-if="column.dataIndex === 'promotionTaskRate'">
                <div class="editable-cell" v-if="record?.promotionTaskRate !== null">
                  {{record?.promotionTaskRate}}%
                </div>
                <div class="editable-cell" v-else>
                  --
                </div>
              </template>
              <template v-else-if="column.dataIndex === 'incentiveStandard'">
                <div class="editable-cell" v-if="record?.incentiveStandard !== null">
                  {{record?.incentiveStandard}}%
                </div>
                <div class="editable-cell" v-else>
                  --
                </div>
              </template>

              <template v-else-if="column.staticOptions?.length">
                <span :style="executeListStyle(record, column?.listStyle)">
                  {{
                    column.staticOptions.filter((x) => x.value === record[column.dataIndex])[0]?.label
                  }}
                </span>
              </template>
              <template v-else-if="column.dataIndex && column?.listStyle">
            <span :style="executeListStyle(record, column?.listStyle)">{{
                record[column.dataIndex]
              }}</span>
              </template>
            </template>
          </BasicTable>
        </a-tab-pane>
        <a-tab-pane key="2" tab="季度业绩达成激励汇总表" >
          <BasicTable 
            @register="registerQuarterTable" 
            isMenuTable ref="tableQuarterRef"
            :row-selection="{
              selectedRowKeys: selectedKeysQuarter,
              onChange: onSelectChangeQuarter,
              getCheckboxProps: (record) => ({
                disabled: !(record.yesOrNo === '0' && record._originalContractTarget === null && record.modifyUserId !== null)
              })
            }"
          >
            <template #toolbar>
              <template v-for="button in tableButtonConfig" :key="button.code">
                <a-button
                  v-if="button.isDefault"
                  type="primary"
                  v-auth="`performanceIncentive:${button.code}`"
                  @click="buttonClick(button.code)"
                >
                  <template #icon><Icon :icon="button.icon" /></template>
                  {{ button.name }}
                </a-button>
                <a-button v-else type="primary">
                  <template #icon><Icon :icon="button.icon" /></template>
                  {{ button.name }}
                </a-button>
              </template>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'yearMonths'">
                {{record.yearQuarter}}
              </template>

              <template v-if="column.dataIndex === 'action'">
                <TableAction :actions="getActions(record)" />
              </template>

              <template v-else-if="column.dataIndex === 'contractTarget'">
                <div class="editable-cell">
                  <a-input-number
                    v-model:value="record.contractTarget"
                    placeholder="请输入"
                    :disabled="!(record.yesOrNo === '0' && record.ruleLevel === 1 && record._originalContractTarget === null && record.modifyUserId !== null)"
                  />
                </div>
              </template>

              <template v-else-if="column.dataIndex === 'incentiveStandard'">
                <div class="editable-cell">
                  <a-input-number
                    v-model:value="record.incentiveStandard"
                    placeholder="请输入"
                    :disabled="!(record.yesOrNo === '0' && record.ruleLevel === 2 && record._originalContractTarget === null && record.modifyUserId !== null)"
                  />
                  %
                </div>
              </template>

              <template v-else-if="column.dataIndex === 'totalPromotionTaskCompletion'">
                <div class="editable-cell">
                  <a-input-number
                    v-model:value="record.totalPromotionTaskCompletion"
                    placeholder="请输入"
                    :disabled="!(record.yesOrNo === '0' && record.ruleLevel === 2 && record._originalContractTarget === null && record.modifyUserId !== null)"
                  />
                </div>
              </template>

              <template v-else-if="column.dataIndex === 'promotionTaskRate'">
                <div class="editable-cell" v-if="record?.promotionTaskRate !== null">
                  {{record?.promotionTaskRate}}%
                </div>
                <div class="editable-cell" v-else>
                  --
                </div>
              </template>

              <template v-else-if="column.staticOptions?.length">
            <span :style="executeListStyle(record, column?.listStyle)">
              {{
                column.staticOptions.filter((x) => x.value === record[column.dataIndex])[0]?.label
              }}
            </span>
              </template>
              <template v-else-if="column.dataIndex && column?.listStyle">
            <span :style="executeListStyle(record, column?.listStyle)">{{
                record[column.dataIndex]
              }}</span>
              </template>
            </template>
          </BasicTable>
        </a-tab-pane>
        <a-tab-pane key="3" tab="年度业绩达成激励汇总表" >
          <BasicTable 
            @register="registerYearTable" 
            isMenuTable ref="tableYearRef"
            :row-selection="{
              selectedRowKeys: selectedKeysYear,
              onChange: onSelectChangeYear,
              getCheckboxProps: (record) => ({
                 disabled: !(record.yesOrNo === '0' && record._originalContractTarget === null && record.modifyUserId !== null)
              })
            }"
          >
            <template #toolbar>
              <template v-for="button in tableButtonConfig" :key="button.code">
                <a-button
                  v-if="button.isDefault"
                  type="primary"
                  @click="buttonClick(button.code)"
                >
                  <template #icon><Icon :icon="button.icon" /></template>
                  {{ button.name }}
                </a-button>
                <a-button v-else type="primary">
                  <template #icon><Icon :icon="button.icon" /></template>
                  {{ button.name }}
                </a-button>
              </template>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'yearMonths'">
                {{record.year}}
              </template>

              <template v-if="column.dataIndex === 'action'">
                <TableAction :actions="getActions(record)" />
              </template>

              <template v-else-if="column.dataIndex === 'contractTarget'">
                <div class="editable-cell">
                  <a-input-number
                    v-model:value="record.contractTarget"
                    placeholder="请输入"
                    :disabled="!(record.yesOrNo === '0' && record.ruleLevel === 1 && record._originalContractTarget === null && record.modifyUserId !== null)"
                  />
                </div>
              </template>

              <template v-else-if="column.dataIndex === 'performanceIncentive'">
                <div class="editable-cell">
                  <a-input-number
                    v-model:value="record.performanceIncentive"
                    placeholder="请输入"
                    :disabled="!(record.yesOrNo === '0' && record.ruleLevel === 3 && record._originalContractTarget === null && record.modifyUserId !== null)"
                  />
                </div>
              </template>

              <template v-else-if="column.dataIndex === 'promotionTaskRate'">
                <div class="editable-cell" v-if="record?.promotionTaskRate !== null">
                  {{record?.promotionTaskRate}}%
                </div>
                <div class="editable-cell" v-else>
                  --
                </div>
              </template>
              <template v-else-if="column.dataIndex === 'incentiveStandard'">
                <div class="editable-cell" v-if="record?.incentiveStandard !== null">
                  {{record?.incentiveStandard}}%
                </div>
                <div class="editable-cell" v-else>
                  --
                </div>
              </template>

              <template v-else-if="column.staticOptions?.length">
            <span :style="executeListStyle(record, column?.listStyle)">
              {{
                column.staticOptions.filter((x) => x.value === record[column.dataIndex])[0]?.label
              }}
            </span>
              </template>
              <template v-else-if="column.dataIndex && column?.listStyle">
            <span :style="executeListStyle(record, column?.listStyle)">{{
                record[column.dataIndex]
              }}</span>
              </template>
            </template>
          </BasicTable>
        </a-tab-pane>
      </a-tabs>

    <PerformanceIncentiveModal @register="registerModal" @success="handleSuccess" />
    <BatchChangeModal v-model:open-modal-visible="openBatchChangeModalVisible" :data="batchChangeData" @success="handleUpdateSuccess"/>

    <AllocationModal v-model:open-modal-visible="openAllocationModalVisible"/>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed, nextTick } from 'vue';
import { BasicTable, useTable, TableAction, ActionItem } from '/@/components/Table';
import { useMessage } from '/@/hooks/web/useMessage';
import { usePermission } from '/@/hooks/web/usePermission';
import { executeListStyle } from '/@/hooks/web/useListStyle';
import { useRouter } from 'vue-router';

import { useModal } from '/@/components/Modal';

import PerformanceIncentiveModal from './components/PerformanceIncentiveModal.vue';
import AllocationModal from './components/AllocationModal.vue';

import { downloadByData } from '/@/utils/file/download';

import {
  monthColumns,
  quarterColumns,
  yearColumns,
  searchFormMonthSchema, searchFormQuarterSchema, searchFormYearSchema,
} from './components/config';
import Icon from '/@/components/Icon/index';
import {
  getMonthPage,
  getQuarterPage,
  getYearPage, monthExportInvoicing,
  quarterExportInvoicing, updateMonth, updateQuarter, updateYear, yearExportInvoicing,
} from '/@/api/performance/PerformanceIncentive';
import BatchChangeModal from './components/BatchChangeModal.vue';
const [registerImportModal, { openModal: openImportModal }] = useModal();
const { notification } = useMessage();
defineEmits(['register']);
const { filterColumnAuth, filterButtonAuth } = usePermission();

// const filterColumns = filterColumnAuth(columns);
const tableMonthRef = ref();
const tableQuarterRef = ref();
const tableYearRef = ref();
const pageParamsInfo = ref<any>({});
const openAllocationModalVisible = ref(false);
const activeKey = ref('1');
// 为每个表格创建独立的选择状态
const selectedKeysMonth = ref<string[]>([]);
const selectedKeysQuarter = ref<string[]>([]);
const selectedKeysYear = ref<string[]>([]);

const selectedRowsDataMonth = ref<any[]>([]);
const selectedRowsDataQuarter = ref<any[]>([]);
const selectedRowsDataYear = ref<any[]>([]);

// 根据当前标签页返回对应的选择状态
const selectedKeys = computed(() => {
  switch (activeKey.value) {
    case '1': return selectedKeysMonth.value;
    case '2': return selectedKeysQuarter.value;
    case '3': return selectedKeysYear.value;
    default: return [];
  }
});

const selectedRowsData = computed(() => {
  switch (activeKey.value) {
    case '1': return selectedRowsDataMonth.value;
    case '2': return selectedRowsDataQuarter.value;
    case '3': return selectedRowsDataYear.value;
    default: return [];
  }
});
const openBatchChangeModalVisible = ref(false);
const batchChangeData = ref({});

//展示在列表内的按钮
const actionButtons = ref<string[]>([
  'update',
  'edit',
]);
const buttonConfigs = computed(() => {
  const list = [
    {
      isUse: true,
      name: '修改签约指标',
      code: 'update',
      icon: 'ant-design:save-outlined',
      isDefault: true },
    {
      isUse: true,
      name: '快速导出',
      code: 'export',
      icon: 'ant-design:export-outlined',
      isDefault: true,
    },
    {
      isUse: true,
      name: '批量修改签约指标',
      code: 'batchUpdate',
      icon: 'ant-design:check-circle-outlined',
      isDefault: true,
    },
  ];
  return filterButtonAuth(list);
});

const tableButtonConfig = computed(() => {
  return buttonConfigs.value?.filter((x) => !actionButtons.value.includes(x.code));
});

const actionButtonConfig = computed(() => {
  return buttonConfigs.value?.filter((x) => actionButtons.value.includes(x.code));
});

const btnEvent = {
  update: handleUpdate,
  export: handleExport,
  batchUpdate: handleBatchUpdate,
};

const { currentRoute } = useRouter();

const formIdComputedRef = computed(() => currentRoute.value.meta.formId as string);

const [registerModal, { openModal }] = useModal();

// 月度表格配置修改
const [registerMonthTable, { reload: reloadMonthTable, getForm: getMonthForm }] = useTable({
  title: '',
  api: getMonthPage,
  rowKey: 'id',
  columns: monthColumns,
  formConfig: {
    rowProps: {
      gutter: 16,
    },
    schemas: searchFormMonthSchema,
    fieldMapToTime: [],
    showResetButton: true,
    showAdvancedButton: false, // 禁用折叠按钮
    autoAdvancedLine: 10, // 增加显示行数
    alwaysShowLines: 10, // 确保显示所有字段
    compact: false, // 禁用紧凑模式
    baseColProps: {
      span: 4, // 每列占4份，确保5个字段能在一行显示
    },
  },
  beforeFetch: (params) => {
    if (params.yearMonths && params.yearMonths.length > 0) {
      params.yearMonthsStart = params.yearMonths[0];
      params.yearMonthsEnd = params.yearMonths[1];
    }
    pageParamsInfo.value = { ...params, FormId: formIdComputedRef.value, PK: 'id' };
    return pageParamsInfo.value;
  },
  afterFetch: (res) => {
    // 设置原始contractTarget值，用于判断显示模式
    if (res) {
      res.forEach(item => {
        item._originalContractTarget = item.contractTarget;
      });
    }
    tableMonthRef.value.setToolBarWidth();
  },
  useSearchForm: true,
  showTableSetting: true,
  actionColumn: {
    width: 160,
    title: '操作',
    dataIndex: 'action',
    slots: { customRender: 'action' },
  },
  striped: false,
  tableSetting: {
    size: false,
  },
  isAdvancedQuery: false,
  querySelectOption: JSON.stringify(searchFormMonthSchema),
  objectId: formIdComputedRef.value,
});

// 季度表格配置修改
const [registerQuarterTable, { reload: reloadQuarterTable, getForm: getQuarterForm }] = useTable({
  title: '',
  api: getQuarterPage,
  rowKey: 'id',
  columns: quarterColumns ,
  formConfig: {
    rowProps: {
      gutter: 16,
    },
    schemas: searchFormQuarterSchema,
    fieldMapToTime: [],
    showResetButton: true,
    showAdvancedButton: false, // 禁用折叠按钮
    autoAdvancedLine: 10, // 增加显示行数
    alwaysShowLines: 10, // 确保显示所有字段
    compact: false, // 禁用紧凑模式
    baseColProps: {
      span: 4, // 每列占4份，确保5个字段能在一行显示
    },
  },
  beforeFetch: (params) => {
    //将01替换成Q1,04替换成Q2,07替换成Q3,10替换成Q4
    if (params.yearMonths) {
      params.yearMonths = params.yearMonths.replace('01', 'Q1');
      params.yearMonths = params.yearMonths.replace('04', 'Q2');
      params.yearMonths = params.yearMonths.replace('07', 'Q3');
      params.yearMonths = params.yearMonths.replace('10', 'Q4');
    }
    params.yearQuarterStart = params.yearMonths;
    params.yearQuarterEnd = params.yearMonths;
    pageParamsInfo.value = { ...params, FormId: formIdComputedRef.value, PK: 'id' };
    return pageParamsInfo.value;
  },
  afterFetch: (res) => {
    // 设置原始contractTarget值，用于判断显示模式
    if (res) {
      res.forEach(item => {
        item._originalContractTarget = item.contractTarget;
      });
    }
    tableQuarterRef.value.setToolBarWidth();
  },
  actionColumn: {
    width: 160,
    title: '操作',
    dataIndex: 'action',
    slots: { customRender: 'action' },
  },
  useSearchForm: true,
  showTableSetting: true,
  striped: false,
  tableSetting: {
    size: false,
  },
  isAdvancedQuery: false,
  querySelectOption: JSON.stringify(searchFormQuarterSchema),
  objectId: formIdComputedRef.value,
  immediate: false,
});

// 年度表格配置修改
const [registerYearTable, { reload: reloadYearTable, getForm: getYearForm }] = useTable({
  title: '',
  api: getYearPage,
  rowKey: 'id',
  columns: yearColumns,
  formConfig: {
    rowProps: {
      gutter: 16,
    },
    schemas: searchFormYearSchema,
    fieldMapToTime: [],
    showResetButton: true,
    showAdvancedButton: false, // 禁用折叠按钮
    autoAdvancedLine: 10, // 增加显示行数
    alwaysShowLines: 10, // 确保显示所有字段
    compact: false, // 禁用紧凑模式
    baseColProps: {
      span: 4, // 每列占4份，确保5个字段能在一行显示
    },
  },
  beforeFetch: (params) => {
    if (params.yearMonths && params.yearMonths.length > 0) {
      params.yearStart = params.yearMonths[0];
      params.yearEnd = params.yearMonths[1];
    }
    pageParamsInfo.value = { ...params, FormId: formIdComputedRef.value, PK: 'id' };
    return pageParamsInfo.value;
  },
  afterFetch: (res) => {
    // 设置原始contractTarget值，用于判断显示模式
    if (res) {
      res.forEach(item => {
        item._originalContractTarget = item.contractTarget;
      });
    }
    tableYearRef.value.setToolBarWidth();
  },
  actionColumn: {
    width: 160,
    title: '操作',
    dataIndex: 'action',
    slots: { customRender: 'action' },
  },
  useSearchForm: true,
  showTableSetting: true,
  striped: false,
  tableSetting: {
    size: false,
  },
  isAdvancedQuery: false,
  querySelectOption: JSON.stringify(searchFormYearSchema),
  objectId: formIdComputedRef.value,
  immediate: false
});

function buttonClick(code) {
  btnEvent[code]();
}

async function handleExport() {
  await handleExportSuccess('');
}

async function handleExportSuccess(cols) {
  const apiList = [monthExportInvoicing, quarterExportInvoicing, yearExportInvoicing];
  const titleList = ['月度业绩达成激励汇总表', '季度业绩达成激励汇总表', '年度业绩达成激励汇总表'];
  const  res = await apiList[Number(activeKey.value) - 1]({
    isTemplate: false,
    columns: cols.toString(),
    ...pageParamsInfo.value,
  });
  downloadByData(
    res.data,
     titleList[Number(activeKey.value) - 1]+'.xlsx',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  );
}

function handleSuccess() {
  reloadMonthTable();
  reloadQuarterTable();
  reloadYearTable()
}

function handleUpdateSuccess() {
  // 根据当前标签页清空对应的选择状态
  if (activeKey.value === '1') {
    selectedKeysMonth.value = [];
    selectedRowsDataMonth.value = [];
    reloadMonthTable();
  } else if (activeKey.value === '2') {
    selectedKeysQuarter.value = [];
    selectedRowsDataQuarter.value = [];
    reloadQuarterTable();
  } else if (activeKey.value === '3') {
    selectedKeysYear.value = [];
    selectedRowsDataYear.value = [];
    reloadYearTable();
  }
}

function handleUpdate(record: Recordable) {
  // 判断contractTarget是否为空
  if (record.contractTarget === null || record.contractTarget === undefined || record.contractTarget === '') {
    notification.warning({
      message: 'Tip',
      description: '请输入签约指标',
    });
    return;
  }

  if (activeKey.value === '1') {
    updateMonth([
      {
        id: record.id,
        contractTarget: record.contractTarget,
      }
    ]).then((res) => {
      if (res.data) {
        notification.success({
          message: '提示',
          description: '修改成功',
        });
      }
      reloadMonthTable();
    });
  } else if (activeKey.value === '2') {
    updateQuarter([
      {
        id: record.id,
        contractTarget: record.contractTarget,
        incentiveStandard: record.incentiveStandard,
        totalPromotionTaskCompletion: record.totalPromotionTaskCompletion
      }
    ]).then((res) => {
      if (res.data) {
        notification.success({
          message: '提示',
          description: '修改成功',
        });
      }
      reloadQuarterTable();
    });
  } else if (activeKey.value === '3') {
    updateYear([
      {
        id: record.id,
        contractTarget: record.contractTarget,
        performanceIncentive: record.performanceIncentive
      }
    ]).then((res) => {
      if (res.data) {
        notification.success({
          message: '提示',
          description: '修改成功',
        });
      }
      reloadYearTable();
    });
  }
}

function handleBatchUpdate() {
  if (selectedKeys.value.length == 0) {
    notification.warning({
      message: 'Tip',
      description: '请选择需要修改的数据',
    });
    return;
  }
  // 根据对应的选中项获取ruleLevel
  let currentSelectedRowsData: any[] = [];
  switch (activeKey.value) {
    case '1':
      currentSelectedRowsData = selectedRowsDataMonth.value;
      break;
    case '2':
      currentSelectedRowsData = selectedRowsDataQuarter.value;
      break;
    case '3':
      currentSelectedRowsData = selectedRowsDataYear.value;
      break;
    default:
      currentSelectedRowsData = [];
  }
  
  batchChangeData.value =  {
    activeKey: activeKey.value,
    ids: selectedKeys.value,
    ruleLevel: currentSelectedRowsData.map(item => item.ruleLevel),
  }
  openBatchChangeModalVisible.value = true;
}

function getActions(record: Recordable): ActionItem[] {
  record.isCanEdit = false;

  const actionsList: ActionItem[] = actionButtonConfig.value?.map((button) => {
    if (!record?.workflowData?.processId) {
      record.isCanEdit = true;
      return {
        icon: button?.icon,
        // auth: `performanceIncentive:${button.code}`,
        tooltip: button?.name,
        color: button.code === 'delete' ? 'error' : undefined,
        onClick: btnEvent[button.code].bind(null, record),
      };
    } else {
      if (button.code === 'view') {
        return {
          icon: button?.icon,
          // auth: `performanceIncentive:${button.code}`,
          tooltip: button?.name,
          onClick: btnEvent[button.code].bind(null, record),
        };
      } else {
        return {};
      }
    }
  });
  return actionsList;
}

function handleTabChange(e) {
  activeKey.value = e;
  // 切换时需要清空选择
  selectedKeysMonth.value = [];
  selectedRowsDataMonth.value = [];
  selectedKeysQuarter.value = [];
  selectedRowsDataQuarter.value = [];
  selectedKeysYear.value = [];
  selectedRowsDataYear.value = [];

  nextTick(() => {
    if (activeKey.value === '1') {
      // reloadMonthTable();
      getMonthForm()?.resetFields();
    } else if (activeKey.value === '2') {
      // reloadQuarterTable();
      getQuarterForm()?.resetFields();
    } else if (activeKey.value === '3') {
      // reloadYearTable();
      getYearForm()?.resetFields();
    }
  });
}

// 为每个表格创建独立的处理函数
function onSelectChangeMonth(selectedRowKeys: [], selectedRows: any) {
  selectedKeysMonth.value = selectedRowKeys;
  selectedRowsDataMonth.value = selectedRows;
}

function onSelectChangeQuarter(selectedRowKeys: [], selectedRows: any) {
  selectedKeysQuarter.value = selectedRowKeys;
  selectedRowsDataQuarter.value = selectedRows;
}

function onSelectChangeYear(selectedRowKeys: [], selectedRows: any) {
  selectedKeysYear.value = selectedRowKeys;
  selectedRowsDataYear.value = selectedRows;
}


function handleAllocation() {
  openAllocationModalVisible.value = true;
}
</script>
<style lang="less" scoped>
.common_box {
  width: 100%;
  height: 100%;
  padding: 8px;
  display: flex;
  flex-direction: column;
  .form_box_btn {
    background-color: #fff;
    display: flex;
    justify-content:end;
    padding: 10px;
  }
  .ant-tabs {
    background-color: #fff;
    padding: 0 16px;
    :deep(> .ant-tabs-nav) {
      margin-bottom: 0 !important;
    }
    height: 100%;
    :deep(.ant-tabs-content) {
      height: 100%;
      padding: 10px 0;
    }
  }
  .main_box {
    flex: auto;
    height: 0;
  }
}
:deep(.ant-table-selection-col) {
  width: 50px;
}
.show {
  display: flex;
}
.hide {
  display: none !important;
}

.editable-cell {
  position: relative;
}

.editable-cell span {
  display: inline-block;
  padding: 4px 11px;
  line-height: 1.5715;
  min-height: 32px;
}

.editable-cell :deep(.ant-input-number) {
  width: 120px;
  min-width: 80px;
}
</style>
