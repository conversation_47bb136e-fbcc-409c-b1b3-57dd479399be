<!-- 模糊搜索组件  -->
<template>
  <div style="position: relative">
    <a-select
      v-model:value="innerValue"
      show-search
      :mode="isMultiple ? 'multiple' : 'null'"
      :disabled="disabled"
      :placeholder="placeholder"
      :field-names="{ label: labelField, value: valueField }"
      :default-active-first-option="false"
      :show-arrow="true"
      :options="searchData"
      :filter-option="false"
      @search="debounceChange"
      @change="handleChange"
      @clear="clear"
      :allowClear="allowClear"
      :getPopupContainer="
        getPopupContainer ? props.getPopupContainer : (triggerNode) => triggerNode.parentNode
      "
    >
      <template v-if="loading" #notFoundContent>
        <a-spin size="small" />
      </template>
    </a-select>
  </div>
</template>
<script lang="ts" setup>
  import { ref, watch, onMounted } from 'vue';
  import { debounce } from 'lodash-es';
  import { defHttp } from '/@/utils/http/axios';
  import { ErrorMessageMode } from '/#/axios';
  const props = defineProps({
    value: { type: [String] }, //绑定值
    rowId: { type: [String] }, //绑定值
    placeholder: { type: String }, //输入框提示语
    url: { type: String }, //接口路径
    disabled: { type: Boolean, default: false },
    params: { type: Object, default: {} },
    getPopupContainer: { type: Function, default: null },
    isView: { type: Boolean, default: false },
    isMultiple: { type: Boolean, default: false }, //是否多选
    allowClear: { type: Boolean, default: false }, //显示清除按钮
    labelField: { type: String, default: 'label' }, 
    valueField: { type: String, default: 'value' }, 
  });

  const loading = ref(false);
  const isUpdate = ref(false);
  const isAdd = ref(false);
  const searchData = ref<any[]>([]);
  const innerValue = ref();
  const isInnerSelect = ref(false);

  const emit = defineEmits(['update:value', 'success', 'change']);
  watch(
    () => props.value,
    (val) => {
      if (isNotEmptyValue(val)) {
        if (props.isMultiple) {
          let tempArr = JSON.parse(JSON.stringify(val));
          innerValue.value = tempArr.split(',');
        } else {
          innerValue.value = val;
        }
        setTimeout(() => {
          // !isNotEmptyValue(props.rowId) &&
          if (!isInnerSelect.value) {
            // console.log("二次赋值")
            loading.value = true;
            getData(props.value, '', props.params);
          } else {
          }
          // 状态置为false
          isInnerSelect.value = false;
        }, 200);
      } else {
        //初始化innerValue值
        if (props.isMultiple) {
          innerValue.value = [];
        } else {
          innerValue.value = [];
        }
      }
    },
    { immediate: true },
  );
  watch(
    () => props.rowId,
    (val) => {
      // console.log('props.rowId:', val)
      if (isNotEmptyValue(val)) {
        isUpdate.value = true;
        isAdd.value = false;
        loading.value = true;
        getData(props.value, '', props.params);
      } else {
        isUpdate.value = false;
        isAdd.value = true;
        setTimeout(() => {
          //  value为空并且 额外参数为空
          if (!isNotEmptyValue(props.value)) {
            if (props.disabled) {
              return;
            }
            console.log('预加载数据');
            loading.value = true;
            getData('', '', props.params);
          } else {
            // console.log('props.value新增初始化数据+默认值', props.value)
            // getData(props.value, '', props.params);
          }
        }, 200);
      }
    },
    { immediate: true },
  );

  watch(
    () => props.params,
    (val) => {
      if (isNotEmptyObject(val)) {
        // 如果是禁用不调用
        if (props.disabled) {
          return;
        }
        setTimeout(() => {
          // 如果value值为空调用
          if (!isNotEmptyValue(props.value)) {
            // console.log('~~根据参数加载~~');
            loading.value = true;
            getData('', '', val);
          }
        }, 350);
      }
    },
    { deep: true },
  );
  // watch(
  //   () => props.getPopupContainer,
  //   (val) => {
  //     console.log(val);
  //   },
  //   { deep: true },
  // );
  async function getUrl(params, mode: ErrorMessageMode = 'modal') {
    return defHttp.get<any[]>(
      {
        url: props.url,
        params,
      },
      {
        errorMessageMode: mode,
      },
    );
  }
  onMounted(async () => {});

  // 获取选择框数据
  async function getData(id: any, name: any, params: any) {
    searchData.value = [];
    let list = await getUrl({ name: name, id: id, ...params });
    searchData.value = list;
    loading.value = false;
  }

  // 关键字搜索
  function handleSearch(val) {
    if (val) {
      searchData.value = [];
      loading.value = true;
      getData('', val, props.params);
    } else {
      getData('', '', props.params);
    }
  }
  const debounceChange = debounce(handleSearch, 500);
  // 选中事件
  const handleChange = (val, option) => {
    if (isNotEmptyValue(val)) {
      isInnerSelect.value = true; //内部选中
      if (props.isMultiple) {
        // 取出label组成字符串
        const labels = option.map((item) => item[props.labelField]).join(',');
        // 取出value组成字符串
        const values = option.map((item) => item[props.valueField]).join(',');
        let tempObj = {
          value: values,
          label: labels,
          option: option,
        };
        emit('update:value', tempObj.value);
        emit('change', tempObj.value, tempObj);
      } else {
        let tempObj = {
          value: option[props.valueField],
          label: option[props.labelField],
          option: option,
        };
        emit('update:value', tempObj.value);
        emit('change', tempObj.value, tempObj);
      }
    } else {
      // console.log("kong",val)
      emit('update:value', '');
      emit('change', '', null);
    }
  };

  // 判断是否有值
  function isNotEmptyValue(value) {
    return (
      value !== '' &&
      value !== null &&
      value !== undefined &&
      (!Array.isArray(value) || value.length > 0) &&
      (!value.trim || value.trim() !== '')
    );
  }

  // 判断对象是否有值
  function isNotEmptyObject(obj) {
    return obj !== null && obj !== undefined && Object.keys(obj).length > 0;
  }

  function clear() {
    // console.log('清空');
    innerValue.value = [];
    emit('update:value', '');
    emit('change', '', null);
    getData('', '', props.params);
  }

  defineExpose({
    getData,
  });
</script>
